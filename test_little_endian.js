/**
 * Test script to verify little-endian encoding is correct
 * This demonstrates the expected behavior for EIP-7002 compliance
 */

// Test case: Convert 1234567890 (0x499602D2) to little-endian
const value = 1234567890;
console.log(`Original value: ${value} (0x${value.toString(16).toUpperCase()})`);

// Expected little-endian bytes for 0x499602D2
// Little-endian: D2 02 96 49 00 00 00 00 (least significant byte first)
const expectedBytes = [0xD2, 0x02, 0x96, 0x49, 0x00, 0x00, 0x00, 0x00];

// Simulate the Solidity function
function uint64ToLittleEndian(value) {
    const result = new Array(8);
    result[0] = value & 0xFF;
    result[1] = (value >> 8) & 0xFF;
    result[2] = (value >> 16) & 0xFF;
    result[3] = (value >> 24) & 0xFF;
    result[4] = (value >> 32) & 0xFF;
    result[5] = (value >> 40) & 0xFF;
    result[6] = (value >> 48) & 0xFF;
    result[7] = (value >> 56) & 0xFF;
    return result;
}

const actualBytes = uint64ToLittleEndian(value);

console.log('Expected little-endian:', expectedBytes.map(b => '0x' + b.toString(16).padStart(2, '0').toUpperCase()).join(' '));
console.log('Actual result:       ', actualBytes.map(b => '0x' + b.toString(16).padStart(2, '0').toUpperCase()).join(' '));

const match = expectedBytes.every((byte, index) => byte === actualBytes[index]);
console.log(`Encoding test: ${match ? '✅ PASS' : '❌ FAIL'}`);

// Verify reversibility
const reversed = actualBytes.reduce((acc, byte, index) => acc + (byte << (index * 8)), 0) >>> 0;
console.log(`Reversibility test: ${reversed === value ? '✅ PASS' : '❌ FAIL'}`);