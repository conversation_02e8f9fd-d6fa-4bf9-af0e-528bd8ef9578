// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * @title IBeaconWithdrawal
 * @notice Interface for the Beacon Withdrawal Precompile
 * @dev This interface interacts with the precompile at address 0x00000961Ef480Eb55e80D19ad83579A64c007002
 *      to handle Type 2 validator withdrawals (0x02 withdrawal credentials)
 */
interface IBeaconWithdrawal {

    /**
     * @notice Emitted when a partial withdrawal is successfully requested
     * @param validator The validator's BLS public key
     * @param amount The amount requested for withdrawal (in wei)
     * @param capsule The address of the capsule that requested the withdrawal
     */
    event PartialWithdrawalRequested(bytes indexed validator, uint256 amount, address indexed capsule);

    /**
     * @notice Emitted when a full withdrawal is successfully requested
     * @param validator The validator's BLS public key
     * @param capsule The address of the capsule that requested the withdrawal
     */
    event FullWithdrawalRequested(bytes indexed validator, address indexed capsule);

    /**
     * @notice Emitted when a withdrawal request fails
     * @param validator The validator's BLS public key
     * @param amount The amount that failed to be withdrawn
     * @param reason The reason for the failure
     */
    event WithdrawalRequestFailed(bytes indexed validator, uint256 amount, string reason);

    /**
     * @notice Request a partial withdrawal for a Type 2 validator
     * @dev This function interacts with the beacon withdrawal precompile
     * @param pubkey The validator's BLS public key (48 bytes)
     * @param amount The amount to withdraw in wei (must be > 0 for partial withdrawal)
     * @return success Whether the withdrawal request was successful
     */
    function requestPartialWithdrawal(bytes calldata pubkey, uint256 amount) external returns (bool success);

    /**
     * @notice Request a full withdrawal for a Type 2 validator (exit staking)
     * @dev This function interacts with the beacon withdrawal precompile with amount = 0
     * @param pubkey The validator's BLS public key (48 bytes)
     * @return success Whether the withdrawal request was successful
     */
    function requestFullWithdrawal(bytes calldata pubkey) external returns (bool success);

    /**
     * @notice Get withdrawal information for a validator
     * @dev Query the current status and available balance for withdrawal
     * @param pubkey The validator's BLS public key (48 bytes)
     * @return isWithdrawable Whether the validator can perform withdrawals
     * @return availableBalance The available balance for withdrawal in wei
     * @return isExited Whether the validator has exited
     */
    function getValidatorWithdrawalInfo(bytes calldata pubkey)
        external
        view
        returns (bool isWithdrawable, uint256 availableBalance, bool isExited);

}
